#!/usr/bin/env python3
"""
实时监控AIHawk系统运行状态
"""

import time
import requests
import subprocess
import sys
from datetime import datetime
import json

def check_service_status():
    """检查服务状态"""
    services = {
        "后端API": "http://localhost:8003/api/health",
        "前端服务": "http://localhost:3001"
    }
    
    status = {}
    for name, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                status[name] = "✅ 运行中"
            else:
                status[name] = f"⚠️ 异常 ({response.status_code})"
        except requests.exceptions.RequestException:
            status[name] = "❌ 离线"
    
    return status

def get_process_info():
    """获取进程信息"""
    try:
        # 检查端口占用情况
        result = subprocess.run(
            ["netstat", "-ano"], 
            capture_output=True, 
            text=True, 
            shell=True
        )
        
        lines = result.stdout.split('\n')
        ports = {}
        
        for line in lines:
            if ':8003' in line and 'LISTENING' in line:
                ports['8003'] = "后端API"
            elif ':3001' in line and 'LISTENING' in line:
                ports['3001'] = "前端服务"
        
        return ports
    except Exception as e:
        return {"error": str(e)}

def display_status():
    """显示系统状态"""
    # 清屏
    subprocess.run("cls" if sys.platform == "win32" else "clear", shell=True)
    
    print("🚀 AI Hawk 4 Job Seeker - Linkedin Jobs - 实时监控")
    print("=" * 60)
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 服务状态
    print("📊 服务状态:")
    status = check_service_status()
    for service, state in status.items():
        print(f"  {service}: {state}")
    print()
    
    # 端口占用
    print("🔌 端口占用:")
    ports = get_process_info()
    if "error" not in ports:
        for port, service in ports.items():
            print(f"  端口 {port}: {service}")
    else:
        print(f"  检查失败: {ports['error']}")
    print()
    
    # 系统信息
    print("💡 访问地址:")
    print("  前端界面: http://localhost:3001")
    print("  后端API:  http://localhost:8003")
    print("  健康检查: http://localhost:8003/api/health")
    print()
    
    # 功能状态
    print("🔧 主要功能:")
    print("  ✅ Gemini 2.5 Flash LLM")
    print("  ✅ 简历优化生成")
    print("  ✅ 求职信生成")
    print("  ✅ LinkedIn自动化")
    print("  ✅ 职位信息解析")
    print()
    
    print("按 Ctrl+C 退出监控")
    print("-" * 60)

def main():
    """主监控循环"""
    try:
        while True:
            display_status()
            time.sleep(5)  # 每5秒刷新一次
    except KeyboardInterrupt:
        print("\n\n👋 监控已停止")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 监控出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
